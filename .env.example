# 环境配置文件示例
# 复制此文件为 .env 并根据实际情况修改配置

# 应用环境
ENVIRONMENT=production

# 端口配置
FRONTEND_PORT=3000
BACKEND_PORT=8001

# 后端配置
API_HOST=0.0.0.0
API_PORT=8001
API_SECRET_KEY=your-secret-key-here

# 数据库配置（如果需要）
DATABASE_URL=sqlite:///./stock_platform.db

# 外部 API 配置
BAOSTOCK_API_TIMEOUT=30

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=/app/logs/app.log

# 安全配置
ALLOWED_HOSTS=localhost,127.0.0.1,your-domain.com
CORS_ORIGINS=http://localhost,https://your-domain.com

# 性能配置
MAX_WORKERS=4
CACHE_TTL=3600

# 监控配置
ENABLE_METRICS=true
METRICS_PORT=9090
