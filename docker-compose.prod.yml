version: '3.8'

services:
  # 后端服务
  backend:
    build:
      context: ./api
      dockerfile: Dockerfile
    container_name: stock-platform-backend-prod
    environment:
      - HOST=0.0.0.0
      - PORT=8001
      - ENVIRONMENT=production
    volumes:
      - ./cases:/app/cases
      - backend_logs:/app/logs
    restart: always
    networks:
      - stock-platform-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8001/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 前端服务
  frontend:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: stock-platform-frontend-prod
    ports:
      - "3000:80"  # 使用 3000 端口避免冲突
      - "3443:443"  # 为 HTTPS 预留，使用 3443 端口
    depends_on:
      - backend
    restart: always
    networks:
      - stock-platform-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx 反向代理（可选，用于更复杂的配置）
  nginx:
    image: nginx:alpine
    container_name: stock-platform-nginx
    ports:
      - "8090:80"  # 使用 8090 端口避免与 8080 冲突
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro  # SSL 证书目录
    depends_on:
      - frontend
      - backend
    restart: always
    networks:
      - stock-platform-network

networks:
  stock-platform-network:
    driver: bridge

volumes:
  backend_logs:
    driver: local
  cases_data:
    driver: local
