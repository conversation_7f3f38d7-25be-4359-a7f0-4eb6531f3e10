name: Simple Deploy (Personal Use)

# 仅在推送到 main 分支时触发
on:
  push:
    branches: [ main, master ]

jobs:
  deploy:
    runs-on: ubuntu-latest
    
    steps:
    - name: Deploy to server
      uses: appleboy/ssh-action@v0.1.5
      with:
        host: ${{ secrets.HOST }}
        username: ${{ secrets.USERNAME }}
        key: ${{ secrets.SSH_KEY }}
        script: |
          # 进入项目目录
          cd ~/a-share-platform-stocks-selection
          
          # 拉取最新代码
          git pull origin main
          
          # 停止现有服务
          docker-compose down
          
          # 重新构建并启动服务
          docker-compose up --build -d
          
          # 等待服务启动
          sleep 10
          
          # 显示服务状态
          docker-compose ps
          
          echo "部署完成！"
          echo "前端访问地址: http://$(curl -s ifconfig.me):3000"
          echo "后端访问地址: http://$(curl -s ifconfig.me):8001"
