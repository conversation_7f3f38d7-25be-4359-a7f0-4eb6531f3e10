# GitHub Actions 自动部署配置
#
# 注意：个人使用建议直接使用 local-deploy.sh 脚本
# 此文件适用于需要自动化部署的团队项目
#
# 使用此配置需要在 GitHub 仓库设置以下 Secrets：
# - HOST: 服务器 IP 地址
# - USERNAME: 服务器用户名
# - SSH_KEY: SSH 私钥
#
# 个人使用推荐：./local-deploy.sh

name: Deploy Stock Platform Scanner (Advanced - Optional)

on:
  push:
    branches: [ main, master ]
  # 移除 pull_request 触发器，避免不必要的构建

jobs:
  # 构建和测试任务
  build-and-test:
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v3

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v2

    - name: Build and test
      run: |
        # 构建镜像（本地测试，不推送到仓库）
        docker-compose build

        # 启动服务进行测试
        docker-compose up -d

        # 等待服务启动
        sleep 30

        # 简单的健康检查
        curl -f http://localhost:3000 || echo "前端健康检查失败，但继续执行"
        curl -f http://localhost:8001/health || echo "后端健康检查失败，但继续执行"

        # 停止测试环境
        docker-compose down

  # 部署任务（仅在推送到 main 分支时执行）
  deploy:
    needs: build-and-test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'

    steps:
    - name: Deploy to server
      uses: appleboy/ssh-action@v0.1.5
      with:
        host: ${{ secrets.HOST }}
        username: ${{ secrets.USERNAME }}
        key: ${{ secrets.SSH_KEY }}
        script: |
          cd /path/to/your/project
          git pull origin main
          docker-compose down
          docker-compose up --build -d
