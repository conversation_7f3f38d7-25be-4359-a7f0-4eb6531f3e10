# --- General ---
# Operating System files
.DS_Store
Thumbs.db
Desktop.ini

# Log files
*.log
logs/

# Environment variables (NEVER commit secrets!)
.env*
!.env.example
!.env.sample

# --- Node.js / Frontend (Vite, Vue) ---
# Dependencies
node_modules/

# Build output
dist/
dist-ssr/ # If using SSR

# Cache directories
.npm/
.vite/
.yarn/cache
.pnpm-store/

# Optional lock files (choose one to commit, ignore others if needed)
# package-lock.json
# yarn.lock
# pnpm-lock.yaml

# Debug logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Coverage directory generated by tools like Vitest/Jest
coverage/
.nyc_output/

# Temporary local files
*.local

# --- Python / Backend (FastAPI in api/ directory) ---
# Byte-compiled / optimized files
api/__pycache__/
api/*.py[cod]
api/*$py.class

# C extensions
api/*.so

# Distribution / packaging
api/*.egg-info/
api/.eggs/
api/sdist/
api/wheelhouse/
api/develop-eggs/
api/parts/
api/pip-wheel-metadata/
api/share/python-wheels/
api/*.manifest
api/pip-log.txt
api/pip-delete-this-directory.txt

# Python Virtual Environments (if created within api/)
api/venv/
api/.venv/
api/env/
api/.env/ # Careful not to clash with root .env* rule if names overlap
api/ENV/
api/virtualenv/
api/lib/
api/lib64/
api/include/
api/Scripts/ # Windows venv
api/bin/    # Unix venv

# Specific package caches or data
api/.ipynb_checkpoints
api/.python-version

# Installer logs
api/pip-log.txt
api/pip-delete-this-directory.txt

# --- IDE / Editor Config ---
.idea/
.vscode/
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw? # Vim swap files

# --- Vercel ---
.vercel/

# --- Other Tools ---
# Example: Terraform state files if used
# *.tfstate*
# .terraform/
baostock_com.md
plan.md

# --- Tests ---
tests/
test_*.py
*_test.py
test_data/
*.test.py
*.test_result.json

# 忽略所有 __pycache__ 文件夹及其中内容
__pycache__/
**/__pycache__/

# 忽略所有 .pyc 文件（Python 编译缓存）
*.py[cod]

*.docx

ref/
cases/